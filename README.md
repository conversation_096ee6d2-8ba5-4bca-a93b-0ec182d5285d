# 德语-中文翻译项目

这是一个基于知识蒸馏的德语到中文翻译项目，使用教师-学生模型架构来创建高效的轻量级翻译模型。

## 项目结构

```
├── data/
│   └── de-zh.txt/                    # 德语-中文数据集
│       ├── bible-uedin.de-zh.de      # 原始德语文件
│       ├── bible-uedin.de-zh.zh      # 原始中文文件
│       ├── dezh.txt                  # 合并后的训练数据
│       ├── dezh_cleaned.txt          # 清理后的训练数据
│       ├── vocab_de.pt               # 德语词汇表缓存
│       ├── vocab_zh.pt               # 中文词汇表缓存
│       ├── tokens_list_de.pt         # 德语token缓存
│       └── tokens_list_zh.pt         # 中文token缓存
├── dataset/
│   └── dezh.py                       # 德语-中文数据集类
├── model/
│   └── distillation_transformer.py  # 知识蒸馏模型定义
├── train_process/
│   └── distillation/                 # 知识蒸馏训练过程目录
├── prepare_dezh_data.py              # 数据预处理脚本
├── clean_dezh_data.py                # 数据清理脚本
├── train.py                          # 主训练脚本（知识蒸馏）
├── translate.py                      # 翻译接口
└── evaluate_models.py               # 模型评估脚本
```

## 数据集信息

- **原始数据**: 62,198 句对
- **清理后数据**: 53,483 句对 (保留率: 86.0%)
- **数据来源**: 圣经德语-中文对照文本
- **德语词汇表大小**: ~21,000 词
- **中文词汇表大小**: ~25,000 词

## 使用方法

### 1. 数据预处理

```bash
# 合并德语和中文文件
python prepare_dezh_data.py

# 清理数据（可选，提高质量）
python clean_dezh_data.py
```

### 2. 训练模型

```bash
# 知识蒸馏训练（两阶段：教师模型 -> 学生模型）
python train.py

# 自定义参数训练
python train.py --batch_size 64 --teacher_epochs 30 --student_epochs 80
```

### 3. 使用翻译接口

```bash
# 交互式翻译
python translate.py

# 单次翻译
python translate.py --text "Guten Morgen!"

# 性能比较（教师 vs 学生模型）
python translate.py --mode compare
```

### 4. 模型评估

```bash
# 评估和比较不同模型的性能
python evaluate_models.py
```

## 模型改进

### 已实现的改进：

1. **数据质量提升**
   - 数据清理和过滤
   - 移除过短或过长的句子
   - 验证德语词汇的存在

2. **训练优化**
   - 标签平滑损失函数
   - 学习率预热和衰减
   - 梯度裁剪
   - 更大的模型维度 (512)
   - 更长的序列长度 (48)

3. **训练策略**
   - 更小的批次大小 (64)
   - 更频繁的模型保存
   - 更多的训练轮次

4. **知识蒸馏技术**
   - 教师-学生模型架构
   - 软标签学习（温度缩放）
   - 蒸馏损失与硬标签损失结合
   - 模型压缩（参数减少约4倍）

## 训练参数

### 改进版训练参数：
- **模型维度**: 512
- **批次大小**: 64
- **最大序列长度**: 48
- **训练轮次**: 200
- **初始学习率**: 0.0001
- **预热步数**: 4000
- **标签平滑**: 0.1

### 知识蒸馏参数：
- **教师模型**: 6层，8头，512维度
- **学生模型**: 3层，4头，256维度
- **蒸馏温度**: 4.0
- **蒸馏权重α**: 0.7
- **压缩比**: ~4倍参数减少
- **教师训练轮次**: 50
- **学生训练轮次**: 100

## 注意事项

1. 训练需要GPU支持以获得合理的训练速度
2. 完整训练可能需要数小时到数天时间
3. 建议监控训练损失，及时停止过拟合
4. 可以根据需要调整训练参数

## 文件说明

- `prepare_dezh_data.py`: 将分离的德语和中文文件合并
- `clean_dezh_data.py`: 清理和过滤数据，提高质量
- `train.py`: 主训练脚本，支持知识蒸馏的完整训练流程
- `translate.py`: 翻译接口，支持交互式翻译和性能比较
- `evaluate_models.py`: 模型性能评估和比较脚本
- `model/distillation_transformer.py`: 知识蒸馏模型定义（教师和学生模型）
- `dataset/dezh.py`: 自定义数据集类，处理德语-中文数据

## 依赖项

- torch
- jieba (中文分词)
- zhconv (中文繁简转换)
- tqdm (进度条)
- tensorboard (训练监控)
