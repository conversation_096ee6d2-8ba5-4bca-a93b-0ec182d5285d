"""
德语-中文翻译模型训练脚本
支持知识蒸馏的完整训练流程
"""
import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from dataset.dezh import DeZhTranslationDataset
from model.distillation_transformer import TeacherTransformerModel, StudentTransformerModel, DistillationLoss
from torch.utils.tensorboard import SummaryWriter
from torch.nn.functional import pad, log_softmax
from tqdm import tqdm
from pathlib import Path
import argparse


class LabelSmoothingLoss(nn.Module):
    """标签平滑损失函数"""
    def __init__(self, smoothing=0.1):
        super(LabelSmoothingLoss, self).__init__()
        self.smoothing = smoothing
        self.padding_idx = 2

    def forward(self, x, target):
        x = log_softmax(x, dim=-1)
        with torch.no_grad():
            true_dist = torch.zeros_like(x)
            true_dist.fill_(self.smoothing / (x.size(-1) - 1))
            true_dist.scatter_(1, target.data.unsqueeze(1), 1.0 - self.smoothing)
            mask = (target == self.padding_idx).nonzero(as_tuple=False)
            if mask.numel() > 0:
                true_dist[mask.squeeze(), :] = 0
        return torch.sum(-true_dist * x, dim=-1).mean()


def collate_fn(batch, max_seq_length, device):
    """数据批处理函数"""
    bs_id = 0  # <bos> index
    eos_id = 1  # <eos> index
    pad_id = 2  # <pad> index

    src_list, tgt_list = [], []

    for _src, _tgt in batch:
        src_tensor = torch.tensor(_src[:max_seq_length - 2], dtype=torch.int64)
        tgt_tensor = torch.tensor(_tgt[:max_seq_length - 2], dtype=torch.int64)

        processed_src = torch.cat([
            torch.tensor([bs_id], dtype=torch.int64),
            src_tensor,
            torch.tensor([eos_id], dtype=torch.int64)
        ])

        processed_tgt = torch.cat([
            torch.tensor([bs_id], dtype=torch.int64),
            tgt_tensor,
            torch.tensor([eos_id], dtype=torch.int64)
        ])

        src_list.append(pad(
            processed_src,
            (0, max_seq_length - len(processed_src)),
            value=pad_id
        ))

        tgt_list.append(pad(
            processed_tgt,
            (0, max_seq_length - len(processed_tgt)),
            value=pad_id
        ))

    src = torch.stack(src_list).to(device)
    tgt = torch.stack(tgt_list).to(device)
    tgt_y = tgt[:, 1:]
    tgt = tgt[:, :-1]

    return src, tgt, tgt_y


def train_teacher(args):
    """训练教师模型"""
    print("=" * 60)
    print("第一阶段：训练教师模型")
    print("=" * 60)
    
    # 设置目录
    work_dir = Path(args.output_dir)
    model_dir = work_dir / "checkpoints"
    work_dir.mkdir(parents=True, exist_ok=True)
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # 数据加载
    dataset = DeZhTranslationDataset(args.data_path)
    train_loader = DataLoader(
        dataset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        collate_fn=lambda batch: collate_fn(batch, args.max_seq_length, args.device)
    )

    # 创建教师模型
    teacher_model = TeacherTransformerModel(
        d_model=args.teacher_dim,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=args.max_seq_length,
        device=args.device,
        num_layers=args.teacher_layers,
        num_heads=args.teacher_heads
    ).to(args.device)

    print(f"教师模型参数数量: {sum(p.numel() for p in teacher_model.parameters()):,}")

    # 训练设置
    criterion = LabelSmoothingLoss(smoothing=0.1)
    optimizer = torch.optim.AdamW(teacher_model.parameters(), lr=args.lr, weight_decay=0.01)
    writer = SummaryWriter(args.output_dir + "/logs/teacher")

    best_loss = float('inf')
    step = 0

    for epoch in range(args.teacher_epochs):
        teacher_model.train()
        epoch_loss = 0
        loop = tqdm(train_loader, desc=f"Teacher Epoch {epoch+1}/{args.teacher_epochs}")
        
        for batch_idx, (src, tgt, tgt_y) in enumerate(loop):
            optimizer.zero_grad()

            out, logits = teacher_model(src, tgt)
            loss = criterion(
                logits.contiguous().view(-1, logits.size(-1)),
                tgt_y.contiguous().view(-1)
            )

            loss.backward()
            torch.nn.utils.clip_grad_norm_(teacher_model.parameters(), max_norm=1.0)
            optimizer.step()

            epoch_loss += loss.item()
            writer.add_scalar('Loss/Teacher', loss.item(), step)
            loop.set_postfix(loss=loss.item())
            step += 1

        avg_loss = epoch_loss / len(train_loader)
        print(f"Teacher Epoch {epoch+1} 平均损失: {avg_loss:.4f}")
        
        if avg_loss < best_loss:
            torch.save(teacher_model, model_dir / 'teacher_best.pt')
            best_loss = avg_loss
            print(f"新的最佳教师模型已保存，损失: {best_loss:.4f}")

    writer.close()
    print("教师模型训练完成！")
    return teacher_model


def train_student(teacher_model, args):
    """知识蒸馏训练学生模型"""
    print("=" * 60)
    print("第二阶段：知识蒸馏训练学生模型")
    print("=" * 60)
    
    # 设置目录
    work_dir = Path(args.output_dir)
    model_dir = work_dir / "checkpoints"
    
    # 数据加载
    dataset = DeZhTranslationDataset(args.data_path)
    train_loader = DataLoader(
        dataset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        collate_fn=lambda batch: collate_fn(batch, args.max_seq_length, args.device)
    )

    # 创建学生模型
    student_model = StudentTransformerModel(
        d_model=args.student_dim,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=args.max_seq_length,
        device=args.device,
        num_layers=args.student_layers,
        num_heads=args.student_heads
    ).to(args.device)

    print(f"学生模型参数数量: {sum(p.numel() for p in student_model.parameters()):,}")
    print(f"参数压缩比: {sum(p.numel() for p in teacher_model.parameters()) / sum(p.numel() for p in student_model.parameters()):.2f}x")

    # 教师模型设为评估模式
    teacher_model.eval()

    # 训练设置
    criterion = DistillationLoss(alpha=args.alpha, temperature=args.temperature)
    optimizer = torch.optim.AdamW(student_model.parameters(), lr=args.lr, weight_decay=0.01)
    writer = SummaryWriter(args.output_dir + "/logs/student")

    best_loss = float('inf')
    step = 0

    for epoch in range(args.student_epochs):
        student_model.train()
        epoch_total_loss = 0
        
        loop = tqdm(train_loader, desc=f"Student Epoch {epoch+1}/{args.student_epochs}")
        
        for batch_idx, (src, tgt, tgt_y) in enumerate(loop):
            optimizer.zero_grad()

            # 教师模型前向传播（不计算梯度）
            with torch.no_grad():
                teacher_out, teacher_logits = teacher_model(src, tgt)

            # 学生模型前向传播
            student_out, student_logits = student_model(src, tgt)

            # 计算蒸馏损失
            total_loss, hard_loss, soft_loss = criterion(
                student_logits.contiguous().view(-1, student_logits.size(-1)),
                teacher_logits.contiguous().view(-1, teacher_logits.size(-1)),
                tgt_y.contiguous().view(-1)
            )

            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(student_model.parameters(), max_norm=1.0)
            optimizer.step()

            epoch_total_loss += total_loss.item()
            writer.add_scalar('Loss/Total', total_loss.item(), step)
            writer.add_scalar('Loss/Hard', hard_loss.item(), step)
            writer.add_scalar('Loss/Soft', soft_loss.item(), step)

            loop.set_postfix({
                'total': total_loss.item(),
                'hard': hard_loss.item(),
                'soft': soft_loss.item()
            })
            step += 1

        avg_loss = epoch_total_loss / len(train_loader)
        print(f"Student Epoch {epoch+1} 总损失: {avg_loss:.4f}")
        
        if avg_loss < best_loss:
            torch.save(student_model, model_dir / 'student_best.pt')
            best_loss = avg_loss
            print(f"新的最佳学生模型已保存，损失: {best_loss:.4f}")

    writer.close()
    print("学生模型蒸馏训练完成！")
    return student_model


def main():
    parser = argparse.ArgumentParser(description='德语-中文翻译模型训练')
    
    # 数据和输出
    parser.add_argument('--data_path', default='data/de-zh.txt/dezh_cleaned.txt', help='训练数据路径')
    parser.add_argument('--output_dir', default='./train_process/distillation', help='输出目录')
    
    # 训练参数
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--max_seq_length', type=int, default=48, help='最大序列长度')
    parser.add_argument('--lr', type=float, default=0.0001, help='学习率')
    parser.add_argument('--teacher_epochs', type=int, default=50, help='教师模型训练轮次')
    parser.add_argument('--student_epochs', type=int, default=100, help='学生模型训练轮次')
    
    # 模型参数
    parser.add_argument('--teacher_dim', type=int, default=512, help='教师模型维度')
    parser.add_argument('--teacher_layers', type=int, default=6, help='教师模型层数')
    parser.add_argument('--teacher_heads', type=int, default=8, help='教师模型注意力头数')
    parser.add_argument('--student_dim', type=int, default=256, help='学生模型维度')
    parser.add_argument('--student_layers', type=int, default=3, help='学生模型层数')
    parser.add_argument('--student_heads', type=int, default=4, help='学生模型注意力头数')
    
    # 蒸馏参数
    parser.add_argument('--alpha', type=float, default=0.7, help='蒸馏损失权重')
    parser.add_argument('--temperature', type=float, default=4.0, help='蒸馏温度')
    
    # 设备
    parser.add_argument('--device', default='auto', help='训练设备')
    
    args = parser.parse_args()
    
    # 设备设置
    if args.device == 'auto':
        args.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    else:
        args.device = torch.device(args.device)
    
    print("开始知识蒸馏训练...")
    print(f"设备: {args.device}")
    print(f"数据路径: {args.data_path}")
    print(f"输出目录: {args.output_dir}")
    print(f"蒸馏参数 - Alpha: {args.alpha}, Temperature: {args.temperature}")
    
    # 第一阶段：训练教师模型
    teacher_model = train_teacher(args)
    
    # 第二阶段：知识蒸馏训练学生模型
    student_model = train_student(teacher_model, args)
    
    print("=" * 60)
    print("训练完成！")
    print("=" * 60)
    print(f"教师模型: {args.output_dir}/checkpoints/teacher_best.pt")
    print(f"学生模型: {args.output_dir}/checkpoints/student_best.pt")


if __name__ == "__main__":
    main()
