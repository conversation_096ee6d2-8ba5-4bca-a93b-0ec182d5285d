"""
德语-中文维基百科标题数据预处理脚本
将分离的德语和中文文件合并为训练格式
"""
import os
from pathlib import Path
from tqdm import tqdm


def merge_de_zh_files(de_file, zh_file, output_file, max_lines=None):
    """
    合并德语和中文文件
    :param de_file: 德语文件路径
    :param zh_file: 中文文件路径
    :param output_file: 输出文件路径
    :param max_lines: 最大处理行数（用于测试）
    """
    print(f"正在合并文件...")
    print(f"德语文件: {de_file}")
    print(f"中文文件: {zh_file}")
    print(f"输出文件: {output_file}")
    
    # 确保输出目录存在
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 统计行数
    with open(de_file, 'r', encoding='utf-8') as f:
        total_lines = sum(1 for _ in f)
    
    if max_lines:
        total_lines = min(total_lines, max_lines)
    
    print(f"总行数: {total_lines:,}")
    
    valid_pairs = 0
    skipped_pairs = 0
    
    with open(de_file, 'r', encoding='utf-8') as de_f, \
         open(zh_file, 'r', encoding='utf-8') as zh_f, \
         open(output_file, 'w', encoding='utf-8') as out_f:
        
        for i, (de_line, zh_line) in enumerate(tqdm(
            zip(de_f, zh_f), 
            total=total_lines, 
            desc="合并数据"
        )):
            if max_lines and i >= max_lines:
                break
                
            de_text = de_line.strip()
            zh_text = zh_line.strip()
            
            # 过滤条件
            if (len(de_text) > 0 and len(zh_text) > 0 and 
                len(de_text) <= 100 and len(zh_text) <= 100 and
                not de_text.startswith('Kategorie:') and
                not zh_text.startswith('Category:') and
                not de_text.startswith('Template:') and
                not zh_text.startswith('Template:')):
                
                # 写入格式：德语\t中文
                out_f.write(f"{de_text}\t{zh_text}\n")
                valid_pairs += 1
            else:
                skipped_pairs += 1
    
    print(f"\n处理完成!")
    print(f"有效句对: {valid_pairs:,}")
    print(f"跳过句对: {skipped_pairs:,}")
    print(f"保留率: {valid_pairs/(valid_pairs+skipped_pairs)*100:.1f}%")
    print(f"输出文件: {output_file}")


def main():
    # 数据路径配置
    data_dir = Path("data/de-zhn.txt")
    de_file = data_dir / "LinguaTools-WikiTitles.de-zh.de"
    zh_file = data_dir / "LinguaTools-WikiTitles.de-zh.zh"
    output_file = data_dir / "dezhn_merged.txt"
    
    # 检查输入文件是否存在
    if not de_file.exists():
        print(f"错误: 德语文件不存在 - {de_file}")
        return
    
    if not zh_file.exists():
        print(f"错误: 中文文件不存在 - {zh_file}")
        return
    
    # 合并文件
    merge_de_zh_files(de_file, zh_file, output_file)
    
    print(f"\n数据预处理完成!")
    print(f"可以使用以下命令开始训练:")
    print(f"python train_teacher.py --data_path {output_file}")
    print(f"python train_student.py --data_path {output_file}")


if __name__ == "__main__":
    main()
