"""
快速训练流程脚本
专门针对有限硬件配置，完整的优化训练流程
"""
import subprocess
import sys
from pathlib import Path
import time
import argparse


def run_command(cmd, description, check_success=True):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {cmd}")
    print('='*60)
    
    start_time = time.time()
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ 成功完成 ({elapsed_time/60:.1f}分钟)")
            if result.stdout:
                # 显示最后几行输出
                lines = result.stdout.strip().split('\n')
                for line in lines[-10:]:
                    print(line)
        else:
            print(f"❌ 执行失败 ({elapsed_time/60:.1f}分钟)")
            print("错误信息:")
            print(result.stderr)
            if check_success:
                return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        if check_success:
            return False
    
    return True


def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if Path(file_path).exists():
        file_size = Path(file_path).stat().st_size / (1024*1024)  # MB
        print(f"✅ {description}: {file_path} ({file_size:.1f}MB)")
        return True
    else:
        print(f"❌ {description}不存在: {file_path}")
        return False


def main():
    parser = argparse.ArgumentParser(description='快速训练流程')
    parser.add_argument('--skip_data_prep', action='store_true', help='跳过数据预处理')
    parser.add_argument('--skip_cleaning', action='store_true', help='跳过数据清洗')
    parser.add_argument('--max_samples', type=int, default=50000, help='最大样本数量')
    parser.add_argument('--teacher_epochs', type=int, default=8, help='教师模型训练轮次')
    parser.add_argument('--student_epochs', type=int, default=12, help='学生模型训练轮次')
    parser.add_argument('--batch_size', type=int, default=64, help='批次大小')
    
    args = parser.parse_args()
    
    print("🚀 德语-中文翻译模型快速训练流程")
    print("="*60)
    print("优化配置:")
    print(f"  最大样本数: {args.max_samples:,}")
    print(f"  教师模型轮次: {args.teacher_epochs}")
    print(f"  学生模型轮次: {args.student_epochs}")
    print(f"  批次大小: {args.batch_size}")
    print("="*60)
    
    total_start_time = time.time()
    
    # 1. 数据预处理
    if not args.skip_data_prep:
        print("\n📁 步骤 1: 数据预处理")
        
        # 检查原始数据文件
        de_file = "data/de-zhn.txt/LinguaTools-WikiTitles.de-zh.de"
        zh_file = "data/de-zhn.txt/LinguaTools-WikiTitles.de-zh.zh"
        
        if not check_file_exists(de_file, "德语数据文件"):
            print("请确保数据文件存在于 data/de-zhn.txt/ 目录下")
            return
        
        if not check_file_exists(zh_file, "中文数据文件"):
            print("请确保数据文件存在于 data/de-zhn.txt/ 目录下")
            return
        
        # 合并数据文件
        if not run_command("python prepare_dezhn_data.py", "合并德语和中文数据文件"):
            return
        
        # 检查合并后的文件
        merged_file = "data/de-zhn.txt/dezhn_merged.txt"
        if not check_file_exists(merged_file, "合并后的数据文件"):
            return
    else:
        print("⏭️  跳过数据预处理")
    
    # 2. 数据清洗
    if not args.skip_cleaning:
        print("\n🧹 步骤 2: 数据清洗和优化")
        
        clean_cmd = (f"python clean_dezhn_data.py "
                    f"--max_samples {args.max_samples} "
                    f"--min_length 2 "
                    f"--max_length 40")
        
        if not run_command(clean_cmd, f"清洗数据（保留{args.max_samples:,}样本）"):
            return
        
        # 检查清洗后的文件
        cleaned_file = "data/de-zhn.txt/dezhn_cleaned.txt"
        if not check_file_exists(cleaned_file, "清洗后的数据文件"):
            return
    else:
        print("⏭️  跳过数据清洗")
    
    # 3. 训练教师模型
    print("\n🎓 步骤 3: 训练优化教师模型")
    
    teacher_cmd = (f"python train_optimized.py "
                  f"--mode teacher "
                  f"--teacher_epochs {args.teacher_epochs} "
                  f"--batch_size {args.batch_size} "
                  f"--use_subset "
                  f"--subset_size {args.max_samples}")
    
    if not run_command(teacher_cmd, f"训练教师模型（{args.teacher_epochs}轮次）"):
        return
    
    # 检查教师模型
    teacher_model = "train_process/optimized/checkpoints/teacher_optimized_best.pt"
    if not check_file_exists(teacher_model, "教师模型文件"):
        return
    
    # 4. 训练学生模型
    print("\n🎒 步骤 4: 训练优化学生模型")
    
    student_cmd = (f"python train_optimized.py "
                  f"--mode student "
                  f"--student_epochs {args.student_epochs} "
                  f"--batch_size {args.batch_size} "
                  f"--use_subset "
                  f"--subset_size {args.max_samples}")
    
    if not run_command(student_cmd, f"训练学生模型（{args.student_epochs}轮次）"):
        return
    
    # 检查学生模型
    student_model = "train_process/optimized/checkpoints/student_optimized_best.pt"
    if not check_file_exists(student_model, "学生模型文件"):
        return
    
    # 5. 测试翻译功能
    print("\n🔍 步骤 5: 测试翻译功能")
    
    # 创建简单的测试脚本
    test_script = '''
import sys
sys.path.append(".")
try:
    from translate import Translator
    
    # 测试学生模型
    translator = Translator(
        "train_process/optimized/checkpoints/student_optimized_best.pt",
        "data/de-zhn.txt/dezhn_cleaned.txt"
    )
    
    test_sentences = [
        "Guten Morgen",
        "Hallo Welt", 
        "Danke schön",
        "Wie geht es dir?",
        "Ich liebe dich"
    ]
    
    print("\\n翻译测试结果:")
    print("-" * 40)
    total_time = 0
    for sentence in test_sentences:
        result, time_taken = translator.translate(sentence)
        total_time += time_taken
        print(f"{sentence:15} -> {result:20} ({time_taken:.3f}s)")
    
    print("-" * 40)
    print(f"平均翻译时间: {total_time/len(test_sentences):.3f}秒")
    print("✅ 翻译功能测试成功")
    
except Exception as e:
    print(f"❌ 翻译功能测试失败: {e}")
    import traceback
    traceback.print_exc()
'''
    
    with open("test_translation_temp.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    run_command("python test_translation_temp.py", "测试翻译功能", check_success=False)
    
    # 清理临时文件
    Path("test_translation_temp.py").unlink(missing_ok=True)
    
    # 总结
    total_time = time.time() - total_start_time
    print("\n" + "="*60)
    print("🎉 快速训练流程完成!")
    print("="*60)
    print(f"总用时: {total_time/60:.1f}分钟")
    print(f"数据样本: {args.max_samples:,}")
    print(f"教师模型: {args.teacher_epochs}轮次")
    print(f"学生模型: {args.student_epochs}轮次")
    
    print("\n📁 生成的文件:")
    print(f"  清洗数据: data/de-zhn.txt/dezhn_cleaned.txt")
    print(f"  教师模型: train_process/optimized/checkpoints/teacher_optimized_best.pt")
    print(f"  学生模型: train_process/optimized/checkpoints/student_optimized_best.pt")
    
    print("\n🚀 现在可以使用翻译器:")
    print("  python translate.py")
    
    print("\n⚡ 如需更长时间训练以提高质量:")
    print(f"  python train_optimized.py --mode both --teacher_epochs 20 --student_epochs 30")
    
    # 估算性能提升
    original_time = 37 * 60  # 37小时转换为分钟
    speedup = original_time / total_time if total_time > 0 else 0
    print(f"\n📈 训练时间优化: 从37小时减少到{total_time/60:.1f}分钟 (提速{speedup:.0f}倍)")


if __name__ == "__main__":
    main()
