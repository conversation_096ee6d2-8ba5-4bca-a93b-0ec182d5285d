import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class PositionalEncoding(nn.Module):
    """Positional encoding for Transformer models."""

    def __init__(self, d_model, dropout, device, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)

        # Initialize positional encoding matrix
        pe = torch.zeros(max_len, d_model).to(device)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1).to(device)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * -(math.log(10000.0) / d_model)).to(device)

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)  # (1, max_len, d_model)
        self.register_buffer("pe", pe)

    def forward(self, x):
        """Add positional encoding to input embeddings."""
        x = x + self.pe[:, :x.size(1)].requires_grad_(False)
        return self.dropout(x)


class TeacherTransformerModel(nn.Module):
    """大型教师Transformer模型"""

    def __init__(self, d_model, src_vocab, tgt_vocab, max_seq_length, device, dropout=0.1, num_layers=6, num_heads=8):
        super(TeacherTransformerModel, self).__init__()
        self.device = device
        self.d_model = d_model

        self.src_embedding = nn.Embedding(len(src_vocab), d_model, padding_idx=2)
        self.tgt_embedding = nn.Embedding(len(tgt_vocab), d_model, padding_idx=2)

        self.positional_encoding = PositionalEncoding(d_model, dropout, device, max_len=max_seq_length)

        self.transformer = nn.Transformer(
            d_model=d_model,
            nhead=num_heads,
            num_encoder_layers=num_layers,
            num_decoder_layers=num_layers,
            dropout=dropout,
            batch_first=True
        )

        self.predictor = nn.Linear(d_model, len(tgt_vocab))

    def forward(self, src, tgt):
        """
        Forward pass of the teacher model.
        Args:
            src: (batch_size, src_seq_len)
            tgt: (batch_size, tgt_seq_len)
        Returns:
            Transformer decoder output and logits
        """
        tgt_mask = nn.Transformer.generate_square_subsequent_mask(tgt.size(1)).to(self.device)
        src_key_padding_mask = self.get_key_padding_mask(src)
        tgt_key_padding_mask = self.get_key_padding_mask(tgt)

        src = self.src_embedding(src) * math.sqrt(self.d_model)
        tgt = self.tgt_embedding(tgt) * math.sqrt(self.d_model)
        src = self.positional_encoding(src)
        tgt = self.positional_encoding(tgt)

        out = self.transformer(
            src, tgt,
            tgt_mask=tgt_mask,
            src_key_padding_mask=src_key_padding_mask,
            tgt_key_padding_mask=tgt_key_padding_mask
        )

        logits = self.predictor(out)
        return out, logits

    @staticmethod
    def get_key_padding_mask(tokens, pad_idx=2):
        """Create key padding mask for transformer."""
        return tokens == pad_idx


class StudentTransformerModel(nn.Module):
    """小型学生Transformer模型"""

    def __init__(self, d_model, src_vocab, tgt_vocab, max_seq_length, device, dropout=0.1, num_layers=3, num_heads=4):
        super(StudentTransformerModel, self).__init__()
        self.device = device
        self.d_model = d_model

        self.src_embedding = nn.Embedding(len(src_vocab), d_model, padding_idx=2)
        self.tgt_embedding = nn.Embedding(len(tgt_vocab), d_model, padding_idx=2)

        self.positional_encoding = PositionalEncoding(d_model, dropout, device, max_len=max_seq_length)

        self.transformer = nn.Transformer(
            d_model=d_model,
            nhead=num_heads,
            num_encoder_layers=num_layers,
            num_decoder_layers=num_layers,
            dropout=dropout,
            batch_first=True
        )

        self.predictor = nn.Linear(d_model, len(tgt_vocab))

    def forward(self, src, tgt):
        """
        Forward pass of the student model.
        Args:
            src: (batch_size, src_seq_len)
            tgt: (batch_size, tgt_seq_len)
        Returns:
            Transformer decoder output and logits
        """
        tgt_mask = nn.Transformer.generate_square_subsequent_mask(tgt.size(1)).to(self.device)
        src_key_padding_mask = self.get_key_padding_mask(src)
        tgt_key_padding_mask = self.get_key_padding_mask(tgt)

        src = self.src_embedding(src) * math.sqrt(self.d_model)
        tgt = self.tgt_embedding(tgt) * math.sqrt(self.d_model)
        src = self.positional_encoding(src)
        tgt = self.positional_encoding(tgt)

        out = self.transformer(
            src, tgt,
            tgt_mask=tgt_mask,
            src_key_padding_mask=src_key_padding_mask,
            tgt_key_padding_mask=tgt_key_padding_mask
        )

        logits = self.predictor(out)
        return out, logits

    @staticmethod
    def get_key_padding_mask(tokens, pad_idx=2):
        """Create key padding mask for transformer."""
        return tokens == pad_idx


class DistillationLoss(nn.Module):
    """知识蒸馏损失函数"""

    def __init__(self, alpha=0.7, temperature=4.0, padding_idx=2):
        super(DistillationLoss, self).__init__()
        self.alpha = alpha  # 蒸馏损失权重
        self.temperature = temperature  # 温度参数
        self.padding_idx = padding_idx
        self.ce_loss = nn.CrossEntropyLoss(ignore_index=padding_idx)
        self.kl_loss = nn.KLDivLoss(reduction='batchmean')

    def forward(self, student_logits, teacher_logits, targets):
        """
        计算蒸馏损失
        Args:
            student_logits: 学生模型输出 (batch_size * seq_len, vocab_size)
            teacher_logits: 教师模型输出 (batch_size * seq_len, vocab_size)
            targets: 真实标签 (batch_size * seq_len)
        Returns:
            总损失
        """
        # 硬标签损失（学生模型与真实标签）
        hard_loss = self.ce_loss(student_logits, targets)

        # 软标签损失（学生模型与教师模型）
        # 使用温度缩放的softmax
        teacher_probs = F.softmax(teacher_logits / self.temperature, dim=-1)
        student_log_probs = F.log_softmax(student_logits / self.temperature, dim=-1)
        
        # 创建mask来忽略padding位置
        mask = (targets != self.padding_idx).float()
        
        # 计算KL散度，只考虑非padding位置
        soft_loss = self.kl_loss(student_log_probs, teacher_probs) * (self.temperature ** 2)

        # 总损失 = α * 蒸馏损失 + (1-α) * 硬标签损失
        total_loss = self.alpha * soft_loss + (1 - self.alpha) * hard_loss

        return total_loss, hard_loss, soft_loss


# 主要模型类别名，用于简化导入
TranslationModel = TeacherTransformerModel


if __name__ == '__main__':
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 添加父目录到路径
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    from dataset.dezh import DeZhTranslationDataset
    dataset = DeZhTranslationDataset("data/de-zh.txt/dezh_cleaned.txt")

    # 创建教师模型（大模型）
    teacher_model = TeacherTransformerModel(
        d_model=512,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=50,
        device=device,
        num_layers=6,
        num_heads=8
    )

    # 创建学生模型（小模型）
    student_model = StudentTransformerModel(
        d_model=256,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=50,
        device=device,
        num_layers=3,
        num_heads=4
    )

    print(f"教师模型参数数量: {sum(p.numel() for p in teacher_model.parameters()):,}")
    print(f"学生模型参数数量: {sum(p.numel() for p in student_model.parameters()):,}")
    print(f"参数压缩比: {sum(p.numel() for p in teacher_model.parameters()) / sum(p.numel() for p in student_model.parameters()):.2f}x")
