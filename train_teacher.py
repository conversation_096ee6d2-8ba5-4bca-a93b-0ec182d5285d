"""
教师模型独立训练脚本
专门用于训练大型教师模型
"""
import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from dataset.dezh import DeZhTranslationDataset
from model.distillation_transformer import TeacherTransformerModel
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm
from pathlib import Path
import argparse
import time


def collate_fn(batch, max_seq_length, device):
    """数据批处理函数"""
    de_batch, zh_batch = zip(*batch)
    
    # 截断或填充到最大长度
    def pad_sequence(sequences, max_len):
        padded = []
        for seq in sequences:
            if len(seq) > max_len:
                padded.append(seq[:max_len])
            else:
                padding = torch.zeros(max_len - len(seq), dtype=seq.dtype)
                padded.append(torch.cat([seq, padding]))
        return torch.stack(padded)
    
    de_padded = pad_sequence(de_batch, max_seq_length).to(device)
    zh_padded = pad_sequence(zh_batch, max_seq_length).to(device)
    
    # 创建目标序列（去掉最后一个token作为输入，去掉第一个token作为标签）
    zh_input = zh_padded[:, :-1]
    zh_target = zh_padded[:, 1:]
    
    return de_padded, zh_input, zh_target


def train_teacher_model(args):
    """训练教师模型"""
    print("=" * 60)
    print("教师模型训练")
    print("=" * 60)
    
    # 设置目录
    work_dir = Path(args.output_dir)
    model_dir = work_dir / "checkpoints"
    work_dir.mkdir(parents=True, exist_ok=True)
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # 数据加载
    print("加载数据集...")
    dataset = DeZhTranslationDataset(args.data_path)
    train_loader = DataLoader(
        dataset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        collate_fn=lambda batch: collate_fn(batch, args.max_seq_length, args.device)
    )
    
    print(f"数据集大小: {len(dataset):,}")
    print(f"德语词汇表大小: {len(dataset.de_vocab):,}")
    print(f"中文词汇表大小: {len(dataset.zh_vocab):,}")
    
    # 创建教师模型
    print("创建教师模型...")
    teacher_model = TeacherTransformerModel(
        d_model=args.d_model,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=args.max_seq_length,
        device=args.device,
        num_layers=args.num_layers,
        num_heads=args.num_heads
    ).to(args.device)
    
    print(f"教师模型参数数量: {sum(p.numel() for p in teacher_model.parameters()):,}")
    
    # 训练设置
    criterion = nn.CrossEntropyLoss(ignore_index=0)  # 忽略padding
    optimizer = torch.optim.AdamW(teacher_model.parameters(), lr=args.lr, weight_decay=0.01)
    
    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5, verbose=True
    )
    
    # TensorBoard
    writer = SummaryWriter(args.output_dir + "/logs/teacher")
    
    best_loss = float('inf')
    step = 0
    start_time = time.time()
    
    print(f"开始训练 - 总轮次: {args.epochs}")
    print(f"设备: {args.device}")
    print(f"批次大小: {args.batch_size}")
    print(f"学习率: {args.lr}")
    
    for epoch in range(args.epochs):
        teacher_model.train()
        epoch_loss = 0
        num_batches = len(train_loader)
        
        loop = tqdm(train_loader, desc=f"Epoch {epoch+1}/{args.epochs}")
        
        for batch_idx, (src, tgt, tgt_y) in enumerate(loop):
            optimizer.zero_grad()
            
            # 前向传播
            out, logits = teacher_model(src, tgt)
            loss = criterion(
                logits.contiguous().view(-1, logits.size(-1)),
                tgt_y.contiguous().view(-1)
            )
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(teacher_model.parameters(), max_norm=1.0)
            optimizer.step()
            
            epoch_loss += loss.item()
            
            # 记录日志
            writer.add_scalar('Loss/Train', loss.item(), step)
            writer.add_scalar('Learning_Rate', optimizer.param_groups[0]['lr'], step)
            
            loop.set_postfix({
                'loss': f"{loss.item():.4f}",
                'avg_loss': f"{epoch_loss/(batch_idx+1):.4f}"
            })
            step += 1
        
        # 计算平均损失
        avg_loss = epoch_loss / num_batches
        scheduler.step(avg_loss)
        
        # 保存最佳模型
        if avg_loss < best_loss:
            best_loss = avg_loss
            torch.save(teacher_model, model_dir / 'teacher_best.pt')
            print(f"\n新的最佳教师模型已保存，损失: {best_loss:.4f}")
        
        # 定期保存检查点
        if (epoch + 1) % 10 == 0:
            torch.save(teacher_model, model_dir / f'teacher_epoch_{epoch+1}.pt')
        
        elapsed_time = time.time() - start_time
        print(f"Epoch {epoch+1} 完成 - 平均损失: {avg_loss:.4f}, 用时: {elapsed_time/60:.1f}分钟")
    
    writer.close()
    
    total_time = time.time() - start_time
    print("\n" + "=" * 60)
    print("教师模型训练完成!")
    print("=" * 60)
    print(f"总训练时间: {total_time/3600:.1f}小时")
    print(f"最佳损失: {best_loss:.4f}")
    print(f"模型保存路径: {model_dir / 'teacher_best.pt'}")
    
    return teacher_model


def main():
    parser = argparse.ArgumentParser(description='教师模型训练')
    
    # 数据和输出
    parser.add_argument('--data_path', default='data/de-zhn.txt/dezhn_merged.txt', help='训练数据路径')
    parser.add_argument('--output_dir', default='./train_process/teacher', help='输出目录')
    
    # 训练参数
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--max_seq_length', type=int, default=48, help='最大序列长度')
    parser.add_argument('--lr', type=float, default=0.0001, help='学习率')
    parser.add_argument('--epochs', type=int, default=50, help='训练轮次')
    
    # 模型参数
    parser.add_argument('--d_model', type=int, default=512, help='模型维度')
    parser.add_argument('--num_layers', type=int, default=6, help='层数')
    parser.add_argument('--num_heads', type=int, default=8, help='注意力头数')
    
    # 设备
    parser.add_argument('--device', default='auto', help='设备')
    
    args = parser.parse_args()
    
    # 设备设置
    if args.device == 'auto':
        args.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    else:
        args.device = torch.device(args.device)
    
    print("教师模型训练配置:")
    print(f"数据路径: {args.data_path}")
    print(f"输出目录: {args.output_dir}")
    print(f"设备: {args.device}")
    print(f"模型参数: {args.d_model}维度, {args.num_layers}层, {args.num_heads}头")
    
    # 检查数据文件
    if not Path(args.data_path).exists():
        print(f"错误: 数据文件不存在 - {args.data_path}")
        print("请先运行: python prepare_dezhn_data.py")
        return
    
    # 开始训练
    train_teacher_model(args)


if __name__ == "__main__":
    main()
