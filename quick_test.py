"""
快速测试脚本
用于验证新的训练流程和数据集
"""
import subprocess
import sys
from pathlib import Path
import time


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {cmd}")
    print('='*60)
    
    start_time = time.time()
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ 成功完成 ({elapsed_time:.1f}秒)")
            if result.stdout:
                print("输出:")
                print(result.stdout[-500:])  # 显示最后500字符
        else:
            print(f"❌ 执行失败 ({elapsed_time:.1f}秒)")
            print("错误信息:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False
    
    return True


def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if Path(file_path).exists():
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}不存在: {file_path}")
        return False


def main():
    print("德语-中文翻译模型快速测试")
    print("="*60)
    
    # 检查数据文件
    print("\n1. 检查数据文件...")
    de_file = "data/de-zhn.txt/LinguaTools-WikiTitles.de-zh.de"
    zh_file = "data/de-zhn.txt/LinguaTools-WikiTitles.de-zh.zh"
    
    if not check_file_exists(de_file, "德语数据文件"):
        print("请确保数据文件存在于 data/de-zhn.txt/ 目录下")
        return
    
    if not check_file_exists(zh_file, "中文数据文件"):
        print("请确保数据文件存在于 data/de-zhn.txt/ 目录下")
        return
    
    # 数据预处理
    print("\n2. 数据预处理...")
    if not run_command("python prepare_dezhn_data.py", "合并德语和中文数据文件"):
        return
    
    # 检查合并后的数据文件
    merged_file = "data/de-zhn.txt/dezhn_merged.txt"
    if not check_file_exists(merged_file, "合并后的数据文件"):
        return
    
    # 快速训练教师模型（少量轮次用于测试）
    print("\n3. 快速训练教师模型...")
    teacher_cmd = ("python train_teacher.py "
                  "--epochs 2 "
                  "--batch_size 16 "
                  "--d_model 128 "
                  "--num_layers 2 "
                  "--num_heads 4")
    
    if not run_command(teacher_cmd, "训练教师模型（测试配置）"):
        return
    
    # 检查教师模型
    teacher_model = "train_process/teacher/checkpoints/teacher_best.pt"
    if not check_file_exists(teacher_model, "教师模型文件"):
        return
    
    # 快速训练学生模型（独立训练）
    print("\n4. 快速训练学生模型（独立训练）...")
    student_cmd = ("python train_student.py "
                  "--mode standalone "
                  "--epochs 2 "
                  "--batch_size 16 "
                  "--d_model 64 "
                  "--num_layers 2 "
                  "--num_heads 2")
    
    if not run_command(student_cmd, "训练学生模型（独立训练，测试配置）"):
        return
    
    # 检查学生模型
    student_model = "train_process/student/checkpoints/student_best.pt"
    if not check_file_exists(student_model, "学生模型文件"):
        return
    
    # 快速知识蒸馏训练
    print("\n5. 快速知识蒸馏训练...")
    distill_cmd = ("python train_student.py "
                  "--mode distillation "
                  "--epochs 2 "
                  "--batch_size 16 "
                  "--d_model 64 "
                  "--num_layers 2 "
                  "--num_heads 2 "
                  "--alpha 0.7 "
                  "--temperature 4.0")
    
    if not run_command(distill_cmd, "知识蒸馏训练（测试配置）"):
        return
    
    # 检查蒸馏模型
    distilled_model = "train_process/student/checkpoints/student_distilled_best.pt"
    if not check_file_exists(distilled_model, "蒸馏学生模型文件"):
        return
    
    # 测试翻译功能
    print("\n6. 测试翻译功能...")
    
    # 创建测试翻译脚本
    test_translate_script = '''
import sys
sys.path.append(".")
from translate import Translator

# 测试学生模型
try:
    translator = Translator(
        "train_process/student/checkpoints/student_best.pt",
        "data/de-zhn.txt/dezhn_merged.txt"
    )
    
    test_sentences = ["Guten Morgen", "Hallo", "Danke"]
    
    print("翻译测试结果:")
    for sentence in test_sentences:
        result, time_taken = translator.translate(sentence)
        print(f"{sentence} -> {result} ({time_taken:.3f}s)")
    
    print("✅ 翻译功能测试成功")
    
except Exception as e:
    print(f"❌ 翻译功能测试失败: {e}")
'''
    
    with open("test_translate_temp.py", "w", encoding="utf-8") as f:
        f.write(test_translate_script)
    
    if not run_command("python test_translate_temp.py", "测试翻译功能"):
        return
    
    # 清理临时文件
    Path("test_translate_temp.py").unlink(missing_ok=True)
    
    # 总结
    print("\n" + "="*60)
    print("🎉 快速测试完成!")
    print("="*60)
    print("✅ 数据预处理成功")
    print("✅ 教师模型训练成功")
    print("✅ 学生模型独立训练成功")
    print("✅ 知识蒸馏训练成功")
    print("✅ 翻译功能测试成功")
    print("\n现在可以进行完整训练:")
    print("1. python train_teacher.py --epochs 50")
    print("2. python train_student.py --mode standalone --epochs 80")
    print("3. python train_student.py --mode distillation --epochs 100")
    print("4. python translate.py")


if __name__ == "__main__":
    main()
