"""
数据预处理脚本：将分离的德语和中文文件合并成训练所需的格式
"""
import os


def prepare_dezh_dataset():
    """
    将德语和中文文件合并成tab分隔的训练数据文件
    """
    # 文件路径
    de_file = "data/de-zh.txt/bible-uedin.de-zh.de"
    zh_file = "data/de-zh.txt/bible-uedin.de-zh.zh"
    output_file = "data/de-zh.txt/dezh.txt"
    
    print("开始处理德语-中文数据集...")
    
    # 检查输入文件是否存在
    if not os.path.exists(de_file):
        print(f"错误：德语文件 {de_file} 不存在")
        return
    
    if not os.path.exists(zh_file):
        print(f"错误：中文文件 {zh_file} 不存在")
        return
    
    # 读取德语文件
    with open(de_file, 'r', encoding='utf-8') as f:
        de_lines = [line.strip() for line in f.readlines()]
    
    # 读取中文文件
    with open(zh_file, 'r', encoding='utf-8') as f:
        zh_lines = [line.strip() for line in f.readlines()]
    
    print(f"德语句子数量: {len(de_lines)}")
    print(f"中文句子数量: {len(zh_lines)}")
    
    # 确保两个文件的行数相同
    if len(de_lines) != len(zh_lines):
        print("警告：德语和中文文件的行数不匹配")
        min_lines = min(len(de_lines), len(zh_lines))
        de_lines = de_lines[:min_lines]
        zh_lines = zh_lines[:min_lines]
        print(f"使用前 {min_lines} 行数据")
    
    # 合并数据并写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        for de_line, zh_line in zip(de_lines, zh_lines):
            # 过滤掉空行
            if de_line.strip() and zh_line.strip():
                f.write(f"{de_line}\t{zh_line}\n")
    
    print(f"数据处理完成！输出文件: {output_file}")
    
    # 显示前几行示例
    print("\n前5行示例:")
    with open(output_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i >= 5:
                break
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                print(f"德语: {parts[0]}")
                print(f"中文: {parts[1]}")
                print("-" * 50)


if __name__ == "__main__":
    prepare_dezh_dataset()
