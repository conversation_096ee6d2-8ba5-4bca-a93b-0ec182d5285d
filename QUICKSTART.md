# 快速开始指南

## 项目概述

这是一个基于知识蒸馏的德语-中文翻译项目，使用教师-学生模型架构来创建高效的轻量级翻译模型。

## 核心特性

- **知识蒸馏**: 使用大型教师模型指导小型学生模型学习
- **模型压缩**: 学生模型参数减少约3-4倍，推理速度提升2-3倍
- **高质量翻译**: 保持接近教师模型的翻译质量
- **简化接口**: 统一的训练和翻译接口

## 快速开始

### 1. 环境准备

```bash
pip install torch jieba zhconv tqdm tensorboard matplotlib seaborn
```

### 2. 数据准备

```bash
# 合并德语和中文文件
python prepare_dezh_data.py

# 清理数据（提高质量）
python clean_dezh_data.py
```

### 3. 训练模型

```bash
# 完整的知识蒸馏训练（推荐）
python train.py

# 快速训练（较少轮次）
python train.py --teacher_epochs 20 --student_epochs 50
```

### 4. 使用翻译

```bash
# 交互式翻译
python translate.py

# 单次翻译
python translate.py --text "Guten Morgen!"

# 性能比较
python translate.py --mode compare
```

## 模型架构

### 教师模型（大模型）
- **维度**: 512
- **层数**: 6层编码器 + 6层解码器
- **注意力头**: 8个
- **参数量**: ~81M

### 学生模型（小模型）
- **维度**: 256
- **层数**: 3层编码器 + 3层解码器
- **注意力头**: 4个
- **参数量**: ~27M
- **压缩比**: 3倍

## 知识蒸馏原理

1. **第一阶段**: 训练大型教师模型，获得高质量的翻译能力
2. **第二阶段**: 使用教师模型的"软标签"指导学生模型学习
3. **损失函数**: 结合硬标签损失和蒸馏损失
4. **温度缩放**: 使用温度参数软化概率分布

## 训练参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--batch_size` | 32 | 批次大小 |
| `--teacher_epochs` | 50 | 教师模型训练轮次 |
| `--student_epochs` | 100 | 学生模型训练轮次 |
| `--alpha` | 0.7 | 蒸馏损失权重 |
| `--temperature` | 4.0 | 蒸馏温度 |
| `--lr` | 0.0001 | 学习率 |

## 性能对比

| 模型 | 参数量 | 推理时间 | 翻译质量 |
|------|--------|----------|----------|
| 教师模型 | 81M | 基准 | 高 |
| 学生模型 | 27M | 2-3x快 | 接近教师模型 |

## 文件结构

```
├── train.py                    # 主训练脚本
├── translate.py                # 翻译接口
├── prepare_dezh_data.py        # 数据预处理
├── clean_dezh_data.py          # 数据清理
├── model/
│   └── distillation_transformer.py  # 模型定义
├── dataset/
│   └── dezh.py                 # 数据集类
└── data/
    └── de-zh.txt/              # 训练数据
```

## 常见问题

### Q: 训练需要多长时间？
A: 在GPU上，教师模型约需要2-4小时，学生模型约需要4-8小时。

### Q: 如何调整模型大小？
A: 修改 `--teacher_dim`, `--student_dim`, `--teacher_layers`, `--student_layers` 等参数。

### Q: 如何提高翻译质量？
A: 1) 增加训练轮次 2) 使用更大的教师模型 3) 调整蒸馏参数

### Q: 学生模型质量不好怎么办？
A: 1) 确保教师模型充分训练 2) 调整温度参数 3) 增加学生模型训练轮次

## 下一步

1. **数据扩展**: 添加更多德语-中文平行语料
2. **模型优化**: 尝试不同的模型架构
3. **多语言支持**: 扩展到其他语言对
4. **部署优化**: 模型量化和加速

## 技术支持

如果遇到问题，请检查：
1. 数据文件是否正确生成
2. GPU内存是否足够
3. 依赖包是否正确安装
4. 模型文件是否存在
