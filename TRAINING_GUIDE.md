# 德语-中文翻译模型训练指南

## 概述

本项目现在支持使用新的德语-中文维基百科标题数据集（de-zhn.txt），并提供了灵活的训练方式：
- 独立训练教师模型和学生模型
- 知识蒸馏训练
- 原有的一体化训练流程

## 数据集信息

### 新数据集：de-zhn.txt
- **数据来源**: 维基百科德语-中文标题对照
- **总数据量**: 约224万句对
- **数据格式**: 分离的德语和中文文件
- **数据特点**: 短语和标题翻译，适合快速训练和测试

### 原数据集：de-zh.txt
- **数据来源**: 圣经德语-中文对照文本
- **数据量**: 约5.3万句对（清理后）
- **数据特点**: 完整句子翻译，语法结构完整

## 训练流程

### 方式一：分步训练（推荐）

#### 1. 数据预处理
```bash
# 合并德语和中文文件
python prepare_dezhn_data.py
```

#### 2. 训练教师模型
```bash
# 基础训练
python train_teacher.py

# 自定义参数训练
python train_teacher.py --epochs 100 --batch_size 64 --d_model 512
```

#### 3. 训练学生模型

**独立训练（不使用知识蒸馏）：**
```bash
python train_student.py --mode standalone
```

**知识蒸馏训练：**
```bash
python train_student.py --mode distillation --teacher_model ./train_process/teacher/checkpoints/teacher_best.pt
```

### 方式二：一体化训练（原有方式）

```bash
# 完整的知识蒸馏流程（教师模型 -> 学生模型）
python train.py
```

## 训练参数说明

### 教师模型参数
- **模型维度**: 512（默认）
- **层数**: 6层
- **注意力头数**: 8头
- **训练轮次**: 50轮（默认）
- **批次大小**: 32（默认）

### 学生模型参数
- **模型维度**: 256（默认）
- **层数**: 3层
- **注意力头数**: 4头
- **训练轮次**: 80轮（默认）
- **压缩比**: 约4倍参数减少

### 知识蒸馏参数
- **蒸馏温度**: 4.0
- **蒸馏权重α**: 0.7
- **硬损失权重**: 0.3
- **软损失权重**: 0.7

## 使用示例

### 快速开始
```bash
# 1. 准备数据
python prepare_dezhn_data.py

# 2. 训练教师模型（约1-2小时）
python train_teacher.py --epochs 30

# 3. 训练学生模型（独立训练，约1小时）
python train_student.py --mode standalone --epochs 50

# 4. 测试翻译
python translate.py
```

### 完整知识蒸馏流程
```bash
# 1. 准备数据
python prepare_dezhn_data.py

# 2. 训练教师模型
python train_teacher.py --epochs 50

# 3. 知识蒸馏训练学生模型
python train_student.py --mode distillation --epochs 100

# 4. 比较模型性能
python translate.py
# 选择模式 3 进行模型比较
```

## 输出目录结构

```
train_process/
├── teacher/                    # 教师模型训练输出
│   ├── checkpoints/
│   │   ├── teacher_best.pt     # 最佳教师模型
│   │   └── teacher_epoch_*.pt  # 定期保存的检查点
│   └── logs/                   # TensorBoard日志
├── student/                    # 学生模型训练输出
│   ├── checkpoints/
│   │   ├── student_best.pt     # 最佳学生模型（独立训练）
│   │   ├── student_distilled_best.pt  # 最佳蒸馏学生模型
│   │   └── student_*.pt        # 检查点文件
│   └── logs/                   # TensorBoard日志
└── distillation/               # 一体化训练输出（原有方式）
    ├── checkpoints/
    └── logs/
```

## 性能监控

### TensorBoard监控
```bash
# 监控教师模型训练
tensorboard --logdir=./train_process/teacher/logs

# 监控学生模型训练
tensorboard --logdir=./train_process/student/logs

# 监控知识蒸馏训练
tensorboard --logdir=./train_process/student/logs/student_distillation
```

### 关键指标
- **训练损失**: 模型收敛情况
- **学习率**: 自动调整情况
- **硬损失**: 学生模型与真实标签的差距
- **软损失**: 学生模型与教师模型的差距

## 模型比较

### 翻译测试
```bash
python translate.py
# 选择不同模式测试：
# 1. 固定测试 - 使用预设句子
# 2. 手动输入 - 交互式测试
# 3. 模型比较 - 教师vs学生性能对比
```

### 性能评估
```bash
python evaluate_models.py
```

## 注意事项

1. **GPU内存**: 教师模型训练需要较多GPU内存，建议使用8GB+显存
2. **训练时间**: 完整训练可能需要数小时，建议先用小数据集测试
3. **数据质量**: 新数据集主要是标题翻译，适合快速实验
4. **模型保存**: 定期保存检查点，避免训练中断丢失进度

## 故障排除

### 常见问题
1. **数据文件不存在**: 确保运行了 `prepare_dezhn_data.py`
2. **GPU内存不足**: 减少批次大小或模型维度
3. **教师模型不存在**: 蒸馏训练前确保教师模型已训练完成

### 调试建议
- 使用小数据集进行快速测试
- 监控TensorBoard日志
- 检查模型输出目录结构
- 验证数据预处理结果
