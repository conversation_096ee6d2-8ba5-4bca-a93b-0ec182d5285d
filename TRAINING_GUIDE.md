# 德语-中文翻译模型训练指南

## 概述

本项目现在支持多种训练方式，特别针对有限硬件配置进行了优化：
- **快速优化训练**：30-60分钟完成训练（推荐）
- **标准训练**：完整的教师-学生模型训练
- **知识蒸馏训练**：高质量的模型压缩

## 🚀 快速开始（推荐）

### 一键快速训练
```bash
# 完整的快速训练流程（约30-60分钟）
python quick_train.py

# 自定义参数的快速训练
python quick_train.py --max_samples 30000 --teacher_epochs 5 --student_epochs 8
```

### 优化配置说明
- **数据量**：5万样本（从220万中筛选）
- **模型大小**：教师256维，学生128维
- **训练轮次**：教师8轮，学生12轮
- **预计时间**：30-60分钟（vs 原来的37-170小时）

## 训练流程详解

### 方式一：快速优化训练（适合有限硬件）

#### 1. 数据预处理和清洗
```bash
# 合并原始数据
python prepare_dezhn_data.py

# 清洗和去重（大幅减少数据量）
python clean_dezhn_data.py --max_samples 50000
```

#### 2. 优化训练
```bash
# 训练优化的教师和学生模型
python train_optimized.py --mode both

# 或分别训练
python train_optimized.py --mode teacher --teacher_epochs 10
python train_optimized.py --mode student --student_epochs 15
```

### 方式二：标准训练（完整配置）

#### 1. 数据预处理
```bash
python prepare_dezhn_data.py
python clean_dezhn_data.py --max_samples 100000
```

#### 2. 分步训练
```bash
# 训练教师模型
python train_teacher.py --epochs 30

# 训练学生模型（独立训练）
python train_student.py --mode standalone --epochs 50

# 或知识蒸馏训练
python train_student.py --mode distillation --epochs 80
```

### 方式三：一体化训练（原有方式）
```bash
# 完整的知识蒸馏流程
python train.py
```

## 优化策略说明

### 数据优化
1. **去重处理**：移除重复的翻译对
2. **质量过滤**：过滤低质量数据
3. **长度限制**：限制序列长度减少内存使用
4. **数据采样**：使用高质量子集进行训练

### 模型优化
1. **模型缩小**：
   - 教师模型：256维，4层，4头
   - 学生模型：128维，2层，2头
2. **序列长度**：从48减少到32
3. **批次大小**：增加到64以提高效率

### 训练优化
1. **轮次减少**：教师8轮，学生12轮
2. **学习率调度**：OneCycleLR快速收敛
3. **梯度裁剪**：防止梯度爆炸
4. **混合精度**：减少内存使用（可选）

## 性能对比

| 配置 | 训练时间 | 模型大小 | 数据量 | 质量 |
|------|----------|----------|--------|------|
| 原始配置 | 37-170小时 | 大 | 220万 | 高 |
| 优化配置 | 30-60分钟 | 小 | 5万 | 中等 |
| 标准配置 | 2-4小时 | 中等 | 10万 | 较高 |

## 使用示例

### 快速测试
```bash
# 一键完成所有步骤
python quick_train.py --max_samples 20000 --teacher_epochs 5

# 测试翻译
python translate.py
```

### 质量优化
```bash
# 使用更多数据和轮次
python clean_dezhn_data.py --max_samples 100000
python train_optimized.py --mode both --teacher_epochs 15 --student_epochs 25
```

## 输出目录结构

```
train_process/
├── optimized/                  # 优化训练输出（推荐）
│   ├── checkpoints/
│   │   ├── teacher_optimized_best.pt
│   │   └── student_optimized_best.pt
│   └── logs/
├── teacher/                    # 标准教师模型
├── student/                    # 标准学生模型
└── distillation/               # 一体化训练
```

## 故障排除

### 常见问题
1. **内存不足**：
   - 减少批次大小：`--batch_size 32`
   - 减少序列长度：`--max_seq_length 24`
   - 使用更小的数据集：`--max_samples 20000`

2. **训练时间过长**：
   - 减少训练轮次：`--teacher_epochs 5 --student_epochs 8`
   - 使用更小的模型：`--teacher_dim 128 --student_dim 64`

3. **质量不满意**：
   - 增加数据量：`--max_samples 100000`
   - 增加训练轮次：`--teacher_epochs 20 --student_epochs 30`
   - 使用标准训练流程

### 硬件建议
- **最低配置**：4GB GPU内存，8GB系统内存
- **推荐配置**：8GB GPU内存，16GB系统内存
- **CPU训练**：可行但速度较慢（约2-3倍时间）

## 监控和评估

### TensorBoard监控
```bash
tensorboard --logdir=./train_process/optimized/logs
```

### 翻译测试
```bash
python translate.py
# 选择不同模式测试翻译质量
```

### 性能评估
```bash
python evaluate_models.py
```

## 进阶优化

### 自定义配置
```bash
# 极速训练（10-20分钟）
python train_optimized.py \
  --teacher_epochs 3 \
  --student_epochs 5 \
  --batch_size 128 \
  --max_seq_length 24 \
  --subset_size 20000

# 平衡配置（1-2小时）
python train_optimized.py \
  --teacher_epochs 15 \
  --student_epochs 25 \
  --batch_size 64 \
  --subset_size 80000
```

### 继续训练
```bash
# 基于已有模型继续训练
python train_optimized.py \
  --mode student \
  --student_epochs 10 \
  --resume_from train_process/optimized/checkpoints/student_optimized_best.pt
```

这个优化方案将训练时间从37-170小时减少到30-60分钟，同时保持合理的翻译质量！
