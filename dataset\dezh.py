import os
import torch
from torch.utils.data import Dataset
from tqdm import tqdm
import jieba
import zhconv
import re
from collections import Counter


class SimpleVocab:
    def __init__(self, tokens, min_freq=1, specials=None):
        if specials is None:
            specials = ["<s>", "</s>", "<pad>", "<unk>"]

        # 统计词频
        counter = Counter()
        for token_list in tokens:
            counter.update(token_list)

        # 构建词汇表
        self.itos = specials.copy()  # index to string
        self.stoi = {token: i for i, token in enumerate(specials)}  # string to index

        for token, freq in counter.items():
            if freq >= min_freq and token not in self.stoi:
                self.stoi[token] = len(self.itos)
                self.itos.append(token)

        self.unk_index = self.stoi["<unk>"]

    def __len__(self):
        return len(self.itos)

    def __getitem__(self, tokens):
        if isinstance(tokens, str):
            return self.stoi.get(tokens, self.unk_index)
        return [self.stoi.get(token, self.unk_index) for token in tokens]

    def lookup_token(self, index):
        return self.itos[index] if 0 <= index < len(self.itos) else "<unk>"

    def lookup_tokens(self, indices):
        return [self.lookup_token(i) for i in indices]


class DeZhTranslationDataset(Dataset):
    def __init__(self, filepath, use_cache=True):
        self.row_count = self.get_row_count(filepath)
        self.use_cache = use_cache

        # 加载词典和token
        self.de_vocab = self.get_de_vocab(filepath)
        self.zh_vocab = self.get_zh_vocab(filepath)
        self.de_tokens = self.load_tokens(filepath, self.de_tokenizer, self.de_vocab, "构建德文tokens", 'de')
        self.zh_tokens = self.load_tokens(filepath, self.zh_tokenizer, self.zh_vocab, "构建中文tokens", 'zh')

    def __getitem__(self, index):
        return self.de_tokens[index], self.zh_tokens[index]

    def __len__(self):
        return self.row_count

    def get_row_count(self, filepath):
        count = 0
        for _ in open(filepath, encoding='utf-8'):
            count += 1
        return count

    def de_tokenizer(self, line):
        # 简单的德语分词：转小写，按空格和标点分割
        line = line.lower()
        # 在标点符号前后添加空格
        line = re.sub(r'([.!?,:;])', r' \1 ', line)
        # 分割并过滤空字符串
        tokens = [token.strip() for token in line.split() if token.strip()]
        return tokens

    def zh_tokenizer(self, line):
        return list(jieba.cut(line))

    def get_de_vocab(self, filepath):
        dir_path = os.path.dirname(filepath)
        de_vocab_file = os.path.join(dir_path, "vocab_de.pt")

        if self.use_cache and os.path.exists(de_vocab_file):
            de_vocab = torch.load(de_vocab_file, map_location="cpu", weights_only=False)
        else:
            print("---开始构建德文词典---")
            all_tokens = []
            with open(filepath, encoding='utf-8') as f:
                for line in tqdm(f, desc="构建德文词典", total=self.row_count):
                    sentence = line.split('\t')
                    if len(sentence) >= 1:
                        german = sentence[0]
                        tokens = self.de_tokenizer(german)
                        all_tokens.append(tokens)

            de_vocab = SimpleVocab(all_tokens, min_freq=2, specials=["<s>", "</s>", "<pad>", "<unk>"])
            if self.use_cache:
                torch.save(de_vocab, de_vocab_file)
        return de_vocab

    def get_zh_vocab(self, filepath):
        dir_path = os.path.dirname(filepath)
        zh_vocab_file = os.path.join(dir_path, "vocab_zh.pt")

        if self.use_cache and os.path.exists(zh_vocab_file):
            zh_vocab = torch.load(zh_vocab_file, map_location="cpu", weights_only=False)
        else:
            print("---开始构建中文词典---")
            all_tokens = []
            with open(filepath, encoding='utf-8') as f:
                for line in tqdm(f, desc="构建中文词典", total=self.row_count):
                    sentence = line.split('\t')
                    if len(sentence) >= 2:
                        chinese = zhconv.convert(sentence[1], 'zh-cn')
                        tokens = self.zh_tokenizer(chinese)
                        all_tokens.append(tokens)

            zh_vocab = SimpleVocab(all_tokens, min_freq=1, specials=["<s>", "</s>", "<pad>", "<unk>"])
            if self.use_cache:
                torch.save(zh_vocab, zh_vocab_file)
        return zh_vocab

    def load_tokens(self, filepath, tokenizer, vocab, desc, lang):
        dir_path = os.path.dirname(filepath)
        cache_file = os.path.join(dir_path, f"tokens_list_{lang}.pt")
        if self.use_cache and os.path.exists(cache_file):
            print(f"正在加载缓存文件[{cache_file}]，请稍候...")
            return torch.load(cache_file, map_location="cpu", weights_only=False)

        tokens_list = []
        with open(filepath, encoding='utf-8') as f:
            for line in tqdm(f, desc=desc, total=self.row_count):
                sentence = line.strip().split('\t')
                if (lang == 'de' and len(sentence) >= 1) or (lang != 'de' and len(sentence) >= 2):
                    if lang == 'de':
                        text = sentence[0].lower()  # 德语转小写
                    else:
                        text = zhconv.convert(sentence[1], 'zh-cn')
                    tokens = tokenizer(text)
                    token_indices = vocab[tokens]  # 使用SimpleVocab的__getitem__方法
                    token_tensor = torch.tensor([vocab["<s>"]] + token_indices + [vocab["</s>"]])
                    tokens_list.append(token_tensor)

        if self.use_cache:
            torch.save(tokens_list, cache_file)
        return tokens_list


if __name__ == '__main__':
    dataset = DeZhTranslationDataset(r"data/de-zh.txt/dezh.txt")
    print("句子数量为:", dataset.row_count)
    print(dataset.de_tokenizer("Ich bin ein deutscher Tokenizer."))
    print("德文词典大小:", len(dataset.de_vocab))
    # 输出德文词典前10个索引
    print(dict((i, dataset.de_vocab.lookup_token(i)) for i in range(10)))
    print("中文词典大小:", len(dataset.zh_vocab))
    # 输出中文词典前10个索引
    print(dict((i, dataset.zh_vocab.lookup_token(i)) for i in range(10)))
    # 输出德文前10个句子对应的字典索引编号
    print(dict((i, dataset.de_tokens[i]) for i in range(10)))
    # 输出中文前10个句子对应的字典索引编号
    print(dict((i, dataset.zh_tokens[i]) for i in range(10)))
