"""
优化的训练脚本
专门针对有限硬件配置，大幅减少训练时间
"""
import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Subset
from dataset.dezh import DeZhTranslationDataset
from model.distillation_transformer import TeacherTransformerModel, StudentTransformerModel, DistillationLoss
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm
from pathlib import Path
import argparse
import time
import random


def collate_fn(batch, max_seq_length, device):
    """优化的数据批处理函数"""
    de_batch, zh_batch = zip(*batch)
    
    def pad_sequence(sequences, max_len):
        padded = []
        for seq in sequences:
            if len(seq) > max_len:
                padded.append(seq[:max_len])
            else:
                padding = torch.zeros(max_len - len(seq), dtype=seq.dtype)
                padded.append(torch.cat([seq, padding]))
        return torch.stack(padded)
    
    de_padded = pad_sequence(de_batch, max_seq_length).to(device)
    zh_padded = pad_sequence(zh_batch, max_seq_length).to(device)
    
    zh_input = zh_padded[:, :-1]
    zh_target = zh_padded[:, 1:]
    
    return de_padded, zh_input, zh_target


def create_optimized_teacher_model(dataset, args):
    """创建优化的教师模型（较小但仍然有效）"""
    return TeacherTransformerModel(
        d_model=args.teacher_dim,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=args.max_seq_length,
        device=args.device,
        num_layers=args.teacher_layers,
        num_heads=args.teacher_heads
    ).to(args.device)


def create_optimized_student_model(dataset, args):
    """创建优化的学生模型（更小更快）"""
    return StudentTransformerModel(
        d_model=args.student_dim,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=args.max_seq_length,
        device=args.device,
        num_layers=args.student_layers,
        num_heads=args.student_heads
    ).to(args.device)


def train_optimized_teacher(args):
    """优化的教师模型训练"""
    print("=" * 60)
    print("优化教师模型训练 - 快速模式")
    print("=" * 60)
    
    # 设置目录
    work_dir = Path(args.output_dir)
    model_dir = work_dir / "checkpoints"
    work_dir.mkdir(parents=True, exist_ok=True)
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # 数据加载
    print("加载数据集...")
    dataset = DeZhTranslationDataset(args.data_path)
    
    # 使用数据子集进行快速训练
    if args.use_subset:
        subset_size = min(args.subset_size, len(dataset))
        indices = random.sample(range(len(dataset)), subset_size)
        dataset = Subset(dataset, indices)
        print(f"使用数据子集: {len(dataset):,} 样本")
    
    train_loader = DataLoader(
        dataset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        collate_fn=lambda batch: collate_fn(batch, args.max_seq_length, args.device),
        num_workers=0,  # 减少内存使用
        pin_memory=False
    )
    
    # 创建优化的教师模型
    print("创建优化教师模型...")
    if args.use_subset:
        # 重新加载完整数据集以获取词汇表
        full_dataset = DeZhTranslationDataset(args.data_path)
        teacher_model = create_optimized_teacher_model(full_dataset, args)
    else:
        teacher_model = create_optimized_teacher_model(dataset, args)
    
    print(f"教师模型参数数量: {sum(p.numel() for p in teacher_model.parameters()):,}")
    
    # 优化的训练设置
    criterion = nn.CrossEntropyLoss(ignore_index=0)
    optimizer = torch.optim.AdamW(
        teacher_model.parameters(), 
        lr=args.lr, 
        weight_decay=0.01,
        eps=1e-6  # 提高数值稳定性
    )
    
    # 更激进的学习率调度
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=args.lr * 2,
        epochs=args.teacher_epochs,
        steps_per_epoch=len(train_loader),
        pct_start=0.1
    )
    
    writer = SummaryWriter(args.output_dir + "/logs/teacher_optimized")
    
    best_loss = float('inf')
    step = 0
    start_time = time.time()
    
    print(f"开始优化训练 - 总轮次: {args.teacher_epochs}")
    print(f"预计训练时间: {estimate_training_time(len(train_loader), args.teacher_epochs)}")
    
    for epoch in range(args.teacher_epochs):
        teacher_model.train()
        epoch_loss = 0
        num_batches = len(train_loader)
        
        loop = tqdm(train_loader, desc=f"Teacher {epoch+1}/{args.teacher_epochs}")
        
        for batch_idx, (src, tgt, tgt_y) in enumerate(loop):
            optimizer.zero_grad()
            
            # 前向传播
            out, logits = teacher_model(src, tgt)
            loss = criterion(
                logits.contiguous().view(-1, logits.size(-1)),
                tgt_y.contiguous().view(-1)
            )
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(teacher_model.parameters(), max_norm=0.5)
            optimizer.step()
            scheduler.step()
            
            epoch_loss += loss.item()
            
            # 减少日志频率
            if step % 50 == 0:
                writer.add_scalar('Loss/Train', loss.item(), step)
                writer.add_scalar('Learning_Rate', optimizer.param_groups[0]['lr'], step)
            
            loop.set_postfix({
                'loss': f"{loss.item():.4f}",
                'lr': f"{optimizer.param_groups[0]['lr']:.6f}"
            })
            step += 1
        
        avg_loss = epoch_loss / num_batches
        
        # 保存最佳模型
        if avg_loss < best_loss:
            best_loss = avg_loss
            torch.save(teacher_model, model_dir / 'teacher_optimized_best.pt')
            print(f"\n新的最佳教师模型已保存，损失: {best_loss:.4f}")
        
        elapsed_time = time.time() - start_time
        remaining_time = (elapsed_time / (epoch + 1)) * (args.teacher_epochs - epoch - 1)
        print(f"Epoch {epoch+1} 完成 - 损失: {avg_loss:.4f}, "
              f"用时: {elapsed_time/60:.1f}分钟, 剩余: {remaining_time/60:.1f}分钟")
    
    writer.close()
    
    total_time = time.time() - start_time
    print("\n" + "="*60)
    print("优化教师模型训练完成!")
    print("="*60)
    print(f"总训练时间: {total_time/60:.1f}分钟")
    print(f"最佳损失: {best_loss:.4f}")
    print(f"模型保存路径: {model_dir / 'teacher_optimized_best.pt'}")
    
    return teacher_model


def train_optimized_student(args):
    """优化的学生模型训练"""
    print("=" * 60)
    print("优化学生模型训练 - 快速模式")
    print("=" * 60)
    
    # 设置目录
    work_dir = Path(args.output_dir)
    model_dir = work_dir / "checkpoints"
    
    # 数据加载
    print("加载数据集...")
    dataset = DeZhTranslationDataset(args.data_path)
    
    # 使用数据子集
    if args.use_subset:
        subset_size = min(args.subset_size, len(dataset))
        indices = random.sample(range(len(dataset)), subset_size)
        dataset_subset = Subset(dataset, indices)
        print(f"使用数据子集: {len(dataset_subset):,} 样本")
    else:
        dataset_subset = dataset
    
    train_loader = DataLoader(
        dataset_subset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        collate_fn=lambda batch: collate_fn(batch, args.max_seq_length, args.device),
        num_workers=0,
        pin_memory=False
    )
    
    # 创建学生模型
    print("创建优化学生模型...")
    student_model = create_optimized_student_model(dataset, args)
    print(f"学生模型参数数量: {sum(p.numel() for p in student_model.parameters()):,}")
    
    # 训练设置
    criterion = nn.CrossEntropyLoss(ignore_index=0)
    optimizer = torch.optim.AdamW(
        student_model.parameters(), 
        lr=args.lr, 
        weight_decay=0.01,
        eps=1e-6
    )
    
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=args.lr * 2,
        epochs=args.student_epochs,
        steps_per_epoch=len(train_loader),
        pct_start=0.1
    )
    
    writer = SummaryWriter(args.output_dir + "/logs/student_optimized")
    
    best_loss = float('inf')
    step = 0
    start_time = time.time()
    
    print(f"开始优化训练 - 总轮次: {args.student_epochs}")
    print(f"预计训练时间: {estimate_training_time(len(train_loader), args.student_epochs)}")
    
    for epoch in range(args.student_epochs):
        student_model.train()
        epoch_loss = 0
        num_batches = len(train_loader)
        
        loop = tqdm(train_loader, desc=f"Student {epoch+1}/{args.student_epochs}")
        
        for batch_idx, (src, tgt, tgt_y) in enumerate(loop):
            optimizer.zero_grad()
            
            out, logits = student_model(src, tgt)
            loss = criterion(
                logits.contiguous().view(-1, logits.size(-1)),
                tgt_y.contiguous().view(-1)
            )
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(student_model.parameters(), max_norm=0.5)
            optimizer.step()
            scheduler.step()
            
            epoch_loss += loss.item()
            
            if step % 50 == 0:
                writer.add_scalar('Loss/Train', loss.item(), step)
                writer.add_scalar('Learning_Rate', optimizer.param_groups[0]['lr'], step)
            
            loop.set_postfix({
                'loss': f"{loss.item():.4f}",
                'lr': f"{optimizer.param_groups[0]['lr']:.6f}"
            })
            step += 1
        
        avg_loss = epoch_loss / num_batches
        
        if avg_loss < best_loss:
            best_loss = avg_loss
            torch.save(student_model, model_dir / 'student_optimized_best.pt')
            print(f"\n新的最佳学生模型已保存，损失: {best_loss:.4f}")
        
        elapsed_time = time.time() - start_time
        remaining_time = (elapsed_time / (epoch + 1)) * (args.student_epochs - epoch - 1)
        print(f"Epoch {epoch+1} 完成 - 损失: {avg_loss:.4f}, "
              f"用时: {elapsed_time/60:.1f}分钟, 剩余: {remaining_time/60:.1f}分钟")
    
    writer.close()
    
    total_time = time.time() - start_time
    print("\n" + "="*60)
    print("优化学生模型训练完成!")
    print("="*60)
    print(f"总训练时间: {total_time/60:.1f}分钟")
    print(f"最佳损失: {best_loss:.4f}")
    print(f"模型保存路径: {model_dir / 'student_optimized_best.pt'}")
    
    return student_model


def estimate_training_time(batches_per_epoch, epochs):
    """估算训练时间"""
    # 基于经验的每批次处理时间（秒）
    time_per_batch = 0.5  # 假设每批次0.5秒
    total_batches = batches_per_epoch * epochs
    total_seconds = total_batches * time_per_batch
    
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    
    if hours > 0:
        return f"约{hours:.0f}小时{minutes:.0f}分钟"
    else:
        return f"约{minutes:.0f}分钟"


def main():
    parser = argparse.ArgumentParser(description='优化训练 - 快速模式')
    
    # 数据和输出
    parser.add_argument('--data_path', default='data/de-zhn.txt/dezhn_cleaned.txt', help='训练数据路径')
    parser.add_argument('--output_dir', default='./train_process/optimized', help='输出目录')
    parser.add_argument('--mode', choices=['teacher', 'student', 'both'], default='both', help='训练模式')
    
    # 优化参数
    parser.add_argument('--use_subset', action='store_true', default=True, help='使用数据子集')
    parser.add_argument('--subset_size', type=int, default=50000, help='数据子集大小')
    parser.add_argument('--batch_size', type=int, default=64, help='批次大小')
    parser.add_argument('--max_seq_length', type=int, default=32, help='最大序列长度（减少内存使用）')
    parser.add_argument('--lr', type=float, default=0.001, help='学习率（提高以加快收敛）')
    
    # 训练轮次（大幅减少）
    parser.add_argument('--teacher_epochs', type=int, default=10, help='教师模型训练轮次')
    parser.add_argument('--student_epochs', type=int, default=15, help='学生模型训练轮次')
    
    # 模型参数（减小模型大小）
    parser.add_argument('--teacher_dim', type=int, default=256, help='教师模型维度')
    parser.add_argument('--teacher_layers', type=int, default=4, help='教师模型层数')
    parser.add_argument('--teacher_heads', type=int, default=4, help='教师模型注意力头数')
    
    parser.add_argument('--student_dim', type=int, default=128, help='学生模型维度')
    parser.add_argument('--student_layers', type=int, default=2, help='学生模型层数')
    parser.add_argument('--student_heads', type=int, default=2, help='学生模型注意力头数')
    
    # 设备
    parser.add_argument('--device', default='auto', help='设备')
    
    args = parser.parse_args()
    
    # 设备设置
    if args.device == 'auto':
        args.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    else:
        args.device = torch.device(args.device)
    
    print("优化训练配置:")
    print(f"数据路径: {args.data_path}")
    print(f"输出目录: {args.output_dir}")
    print(f"设备: {args.device}")
    print(f"使用数据子集: {args.use_subset} ({args.subset_size:,} 样本)" if args.use_subset else "使用完整数据集")
    print(f"教师模型: {args.teacher_dim}维度, {args.teacher_layers}层, {args.teacher_heads}头")
    print(f"学生模型: {args.student_dim}维度, {args.student_layers}层, {args.student_heads}头")
    
    # 检查数据文件
    if not Path(args.data_path).exists():
        print(f"错误: 数据文件不存在 - {args.data_path}")
        print("请先运行: python clean_dezhn_data.py")
        return
    
    # 设置随机种子
    random.seed(42)
    torch.manual_seed(42)
    
    start_time = time.time()
    
    # 根据模式进行训练
    if args.mode in ['teacher', 'both']:
        train_optimized_teacher(args)
    
    if args.mode in ['student', 'both']:
        train_optimized_student(args)
    
    total_time = time.time() - start_time
    print(f"\n🎉 优化训练完成! 总用时: {total_time/60:.1f}分钟")


if __name__ == "__main__":
    main()
