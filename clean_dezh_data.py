"""
数据清理脚本：过滤和清理德语-中文翻译数据
"""
import re
import zhconv


def clean_german_text(text):
    """清理德语文本"""
    # 移除多余的空格
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除特殊字符（保留基本标点）
    text = re.sub(r'[^\w\s.!?,:;-]', '', text)
    
    return text


def clean_chinese_text(text):
    """清理中文文本"""
    # 转换为简体中文
    text = zhconv.convert(text, 'zh-cn')
    
    # 移除多余的空格
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除一些特殊符号
    text = re.sub(r'[〔〕【】]', '', text)
    
    return text


def is_valid_pair(de_text, zh_text):
    """检查句子对是否有效"""
    # 长度检查
    if len(de_text) < 3 or len(zh_text) < 2:
        return False
    
    if len(de_text) > 200 or len(zh_text) > 100:
        return False
    
    # 检查是否包含过多数字
    if len(re.findall(r'\d', de_text)) > len(de_text) * 0.3:
        return False
    
    # 检查德语是否包含基本的德语词汇
    german_words = ['der', 'die', 'das', 'und', 'ist', 'ein', 'eine', 'zu', 'von', 'mit', 'auf', 'für', 'in', 'an', 'bei']
    de_lower = de_text.lower()
    if not any(word in de_lower for word in german_words):
        return False
    
    return True


def clean_dataset():
    """清理数据集"""
    input_file = "data/de-zh.txt/dezh.txt"
    output_file = "data/de-zh.txt/dezh_cleaned.txt"
    
    print("开始清理数据集...")
    
    cleaned_pairs = []
    total_lines = 0
    valid_lines = 0
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            total_lines += 1
            parts = line.strip().split('\t')
            
            if len(parts) >= 2:
                de_text = clean_german_text(parts[0])
                zh_text = clean_chinese_text(parts[1])
                
                if is_valid_pair(de_text, zh_text):
                    cleaned_pairs.append((de_text, zh_text))
                    valid_lines += 1
    
    # 按长度排序，优先使用较短的句子进行训练
    cleaned_pairs.sort(key=lambda x: len(x[0]) + len(x[1]))
    
    # 写入清理后的数据
    with open(output_file, 'w', encoding='utf-8') as f:
        for de_text, zh_text in cleaned_pairs:
            f.write(f"{de_text}\t{zh_text}\n")
    
    print(f"数据清理完成！")
    print(f"原始数据: {total_lines} 行")
    print(f"清理后数据: {valid_lines} 行")
    print(f"保留比例: {valid_lines/total_lines*100:.1f}%")
    print(f"输出文件: {output_file}")
    
    # 显示一些示例
    print("\n清理后的前10行示例:")
    for i, (de_text, zh_text) in enumerate(cleaned_pairs[:10]):
        print(f"{i+1}. DE: {de_text}")
        print(f"   ZH: {zh_text}")
        print("-" * 50)


if __name__ == "__main__":
    clean_dataset()
