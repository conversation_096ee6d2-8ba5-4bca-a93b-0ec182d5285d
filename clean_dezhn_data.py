"""
德语-中文数据清洗脚本
去除重复数据，优化数据质量，减少训练时间
"""
import os
from pathlib import Path
from tqdm import tqdm
from collections import defaultdict
import argparse


def clean_dezhn_data(input_file, output_file, max_samples=None, min_length=2, max_length=50):
    """
    清洗德语-中文数据
    :param input_file: 输入文件路径
    :param output_file: 输出文件路径
    :param max_samples: 最大样本数量（用于控制数据集大小）
    :param min_length: 最小长度过滤
    :param max_length: 最大长度过滤
    """
    print(f"开始清洗数据...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    if max_samples:
        print(f"最大样本数: {max_samples:,}")
    
    # 确保输出目录存在
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 统计原始数据
    with open(input_file, 'r', encoding='utf-8') as f:
        total_lines = sum(1 for _ in f)
    
    print(f"原始数据行数: {total_lines:,}")
    
    # 用于去重的集合
    seen_pairs = set()
    seen_de = defaultdict(set)  # 德语 -> 中文集合
    seen_zh = defaultdict(set)  # 中文 -> 德语集合
    
    # 统计变量
    total_processed = 0
    duplicates = 0
    too_short = 0
    too_long = 0
    invalid_format = 0
    low_quality = 0
    valid_pairs = 0
    
    cleaned_data = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in tqdm(f, total=total_lines, desc="清洗数据"):
            total_processed += 1
            
            # 如果达到最大样本数，停止处理
            if max_samples and valid_pairs >= max_samples:
                break
            
            line = line.strip()
            if not line:
                continue
            
            # 分割德语和中文
            parts = line.split('\t')
            if len(parts) != 2:
                invalid_format += 1
                continue
            
            de_text = parts[0].strip()
            zh_text = parts[1].strip()
            
            # 基本长度过滤
            if len(de_text) < min_length or len(zh_text) < min_length:
                too_short += 1
                continue
            
            if len(de_text) > max_length or len(zh_text) > max_length:
                too_long += 1
                continue
            
            # 质量过滤
            if is_low_quality(de_text, zh_text):
                low_quality += 1
                continue
            
            # 去重检查
            pair_key = (de_text.lower(), zh_text.lower())
            if pair_key in seen_pairs:
                duplicates += 1
                continue
            
            # 检查是否是更好的翻译对
            if should_keep_translation(de_text, zh_text, seen_de, seen_zh):
                seen_pairs.add(pair_key)
                seen_de[de_text.lower()].add(zh_text.lower())
                seen_zh[zh_text.lower()].add(de_text.lower())
                
                cleaned_data.append((de_text, zh_text))
                valid_pairs += 1
            else:
                duplicates += 1
    
    # 进一步优化：选择最佳翻译对
    if len(cleaned_data) > max_samples if max_samples else False:
        print("进行质量排序...")
        cleaned_data = select_best_translations(cleaned_data, max_samples)
        valid_pairs = len(cleaned_data)
    
    # 写入清洗后的数据
    print("写入清洗后的数据...")
    with open(output_file, 'w', encoding='utf-8') as f:
        for de_text, zh_text in tqdm(cleaned_data, desc="写入数据"):
            f.write(f"{de_text}\t{zh_text}\n")
    
    # 统计报告
    print("\n" + "="*60)
    print("数据清洗完成!")
    print("="*60)
    print(f"原始数据: {total_processed:,} 行")
    print(f"有效数据: {valid_pairs:,} 行")
    print(f"数据保留率: {valid_pairs/total_processed*100:.1f}%")
    print(f"数据压缩率: {total_processed/valid_pairs:.1f}x")
    print("\n过滤统计:")
    print(f"  重复数据: {duplicates:,}")
    print(f"  格式错误: {invalid_format:,}")
    print(f"  长度过短: {too_short:,}")
    print(f"  长度过长: {too_long:,}")
    print(f"  质量过低: {low_quality:,}")
    print(f"\n输出文件: {output_file}")


def is_low_quality(de_text, zh_text):
    """判断是否为低质量数据"""
    # 过滤条件
    filters = [
        # 过滤分类和模板
        de_text.startswith('Kategorie:') or zh_text.startswith('Category:'),
        de_text.startswith('Template:') or zh_text.startswith('Template:'),
        
        # 过滤纯数字或符号
        de_text.isdigit() or zh_text.isdigit(),
        
        # 过滤包含过多特殊字符的
        sum(1 for c in de_text if not c.isalnum() and c not in ' -.,()') > len(de_text) * 0.3,
        sum(1 for c in zh_text if not c.isalnum() and c not in ' -.,()（）') > len(zh_text) * 0.3,
        
        # 过滤相同的德语和中文（可能是英文或其他语言）
        de_text.lower() == zh_text.lower(),
        
        # 过滤包含HTML标签的
        '<' in de_text or '>' in de_text or '<' in zh_text or '>' in zh_text,
        
        # 过滤过短的单词（可能是缩写）
        len(de_text.split()) == 1 and len(de_text) <= 3,
    ]
    
    return any(filters)


def should_keep_translation(de_text, zh_text, seen_de, seen_zh):
    """判断是否应该保留这个翻译对"""
    de_lower = de_text.lower()
    zh_lower = zh_text.lower()
    
    # 如果德语已经有翻译，检查是否是更好的翻译
    if de_lower in seen_de:
        existing_zh = seen_de[de_lower]
        # 如果已经有相同的中文翻译，跳过
        if zh_lower in existing_zh:
            return False
        # 如果已经有3个或更多翻译，跳过（避免一对多过多）
        if len(existing_zh) >= 3:
            return False
    
    # 如果中文已经有翻译，检查是否是更好的翻译
    if zh_lower in seen_zh:
        existing_de = seen_zh[zh_lower]
        # 如果已经有相同的德语翻译，跳过
        if de_lower in existing_de:
            return False
        # 如果已经有3个或更多翻译，跳过（避免一对多过多）
        if len(existing_de) >= 3:
            return False
    
    return True


def select_best_translations(data, max_samples):
    """选择最佳翻译对"""
    # 按质量评分排序
    scored_data = []
    for de_text, zh_text in data:
        score = calculate_quality_score(de_text, zh_text)
        scored_data.append((score, de_text, zh_text))
    
    # 按分数降序排序
    scored_data.sort(reverse=True)
    
    # 返回前max_samples个
    return [(de, zh) for _, de, zh in scored_data[:max_samples]]


def calculate_quality_score(de_text, zh_text):
    """计算翻译对的质量分数"""
    score = 0
    
    # 长度适中加分
    de_len = len(de_text.split())
    zh_len = len(zh_text)
    
    if 2 <= de_len <= 10:  # 德语2-10个词
        score += 10
    if 2 <= zh_len <= 20:  # 中文2-20个字符
        score += 10
    
    # 包含常用词汇加分
    common_de_words = {'der', 'die', 'das', 'und', 'ist', 'in', 'zu', 'von', 'mit', 'auf'}
    if any(word.lower() in common_de_words for word in de_text.split()):
        score += 5
    
    # 避免过于复杂的专有名词
    if de_text.count('(') <= 1 and zh_text.count('（') <= 1:
        score += 5
    
    # 长度比例合理
    ratio = len(zh_text) / len(de_text) if len(de_text) > 0 else 0
    if 0.5 <= ratio <= 2.0:
        score += 5
    
    return score


def main():
    parser = argparse.ArgumentParser(description='德语-中文数据清洗')
    parser.add_argument('--input', default='data/de-zhn.txt/dezhn_merged.txt', help='输入文件路径')
    parser.add_argument('--output', default='data/de-zhn.txt/dezhn_cleaned.txt', help='输出文件路径')
    parser.add_argument('--max_samples', type=int, default=100000, help='最大样本数量（默认10万）')
    parser.add_argument('--min_length', type=int, default=2, help='最小长度')
    parser.add_argument('--max_length', type=int, default=50, help='最大长度')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not Path(args.input).exists():
        print(f"错误: 输入文件不存在 - {args.input}")
        print("请先运行: python prepare_dezhn_data.py")
        return
    
    # 清洗数据
    clean_dezhn_data(
        args.input, 
        args.output, 
        args.max_samples,
        args.min_length,
        args.max_length
    )
    
    print(f"\n可以使用清洗后的数据进行训练:")
    print(f"python train_teacher.py --data_path {args.output}")
    print(f"python train_student.py --data_path {args.output}")


if __name__ == "__main__":
    main()
